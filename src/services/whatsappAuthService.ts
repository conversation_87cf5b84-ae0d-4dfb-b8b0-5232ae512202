import { supabase } from '@/integrations/supabase/client';

export interface WhatsAppSignUpData {
  phone: string;
  password: string;
  full_name: string;
}

export interface WhatsAppOTPVerificationData {
  phone: string;
  otp: string;
}

export interface WhatsAppAuthResult {
  success: boolean;
  error?: string;
  user?: any;
  phone?: string;
  expires_in?: number;
}

class WhatsAppAuthService {
  /**
   * Format phone number to international format
   */
  private formatPhoneNumber(phone: string, countryCode: string = '+91'): string {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    
    // If phone starts with country code digits, don't add again
    if (cleanPhone.startsWith('91') && countryCode === '+91') {
      return '+' + cleanPhone;
    }
    
    // Add country code if not present
    if (!cleanPhone.startsWith(countryCode.replace('+', ''))) {
      return countryCode + cleanPhone;
    }
    
    return '+' + cleanPhone;
  }

  /**
   * Validate phone number format
   */
  private validatePhoneNumber(phone: string): boolean {
    // Basic international phone number validation
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    const cleanPhone = phone.replace(/[\s()-]/g, '');
    return phoneRegex.test(cleanPhone);
  }

  /**
   * Send WhatsApp OTP for registration
   */
  async sendWhatsAppOTP(userData: WhatsAppSignUpData): Promise<WhatsAppAuthResult> {
    try {
      // Format and validate phone number
      const formattedPhone = this.formatPhoneNumber(userData.phone);
      
      if (!this.validatePhoneNumber(formattedPhone)) {
        return { 
          success: false, 
          error: 'Invalid phone number format' 
        };
      }

      // Validate other fields
      if (!userData.full_name || userData.full_name.trim().length < 2) {
        return { 
          success: false, 
          error: 'Full name must be at least 2 characters long' 
        };
      }

      if (!userData.password || userData.password.length < 6) {
        return { 
          success: false, 
          error: 'Password must be at least 6 characters long' 
        };
      }

      console.log('Sending WhatsApp OTP to:', formattedPhone);

      // Call send-whatsapp-otp edge function
      const { data, error } = await supabase.functions.invoke('send-whatsapp-otp', {
        body: {
          phone: formattedPhone,
          full_name: userData.full_name.trim(),
          password: userData.password
        }
      });

      if (error) {
        console.error('WhatsApp OTP send error:', error);
        return {
          success: false,
          error: `Failed to send WhatsApp OTP: ${error.message}`
        };
      }

      if (!data.success) {
        console.error('WhatsApp OTP API error:', data);
        return {
          success: false,
          error: data.error || 'Failed to send WhatsApp OTP'
        };
      }

      console.log('WhatsApp OTP sent successfully:', data);
      return { 
        success: true, 
        phone: data.phone,
        expires_in: data.expires_in
      };
    } catch (error) {
      console.error('Error in sendWhatsAppOTP:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Verify WhatsApp OTP and create user account
   */
  async verifyWhatsAppOTP(verificationData: WhatsAppOTPVerificationData): Promise<WhatsAppAuthResult> {
    try {
      // Format phone number
      const formattedPhone = this.formatPhoneNumber(verificationData.phone);

      // Validate OTP format
      if (!/^\d{6}$/.test(verificationData.otp)) {
        return { 
          success: false, 
          error: 'Invalid OTP format. Must be 6 digits.' 
        };
      }

      console.log('Verifying WhatsApp OTP for:', formattedPhone);

      // Call verify-whatsapp-otp edge function
      const { data, error } = await supabase.functions.invoke('verify-whatsapp-otp', {
        body: {
          phone: formattedPhone,
          otp: verificationData.otp
        }
      });

      if (error) {
        console.error('WhatsApp OTP verification error:', error);
        return {
          success: false,
          error: `Failed to verify WhatsApp OTP: ${error.message}`
        };
      }

      if (!data.success) {
        console.error('WhatsApp OTP verification failed:', data);
        return {
          success: false,
          error: data.error || 'OTP verification failed'
        };
      }

      console.log('WhatsApp OTP verified successfully:', data);
      return { 
        success: true, 
        user: data.user
      };
    } catch (error) {
      console.error('Error in verifyWhatsAppOTP:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Resend WhatsApp OTP
   */
  async resendWhatsAppOTP(phone: string): Promise<WhatsAppAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Resending WhatsApp OTP to:', formattedPhone);

      // For resend, we need to get the pending user data first
      // This is a simplified approach - in production, you might want a dedicated resend endpoint
      return { 
        success: false, 
        error: 'Please start the registration process again to resend OTP' 
      };
    } catch (error) {
      console.error('Error in resendWhatsAppOTP:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Login with phone and password (Custom implementation)
   */
  async signInWithPhone(phone: string, password: string): Promise<WhatsAppAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Signing in with phone:', formattedPhone);

      // Call simple phone login edge function
      const { data, error } = await supabase.functions.invoke('phone-login-simple', {
        body: {
          phone: formattedPhone,
          password: password
        }
      });

      if (error) {
        console.error('Phone sign in error:', error);
        return {
          success: false,
          error: `Failed to sign in: ${error.message}`
        };
      }

      if (!data.success) {
        console.error('Phone sign in failed:', data);
        return {
          success: false,
          error: data.error || 'Phone sign in failed'
        };
      }

      // Set the session in Supabase client if provided
      if (data.session) {
        await supabase.auth.setSession(data.session);
      }

      console.log('Phone sign in successful:', data);
      return {
        success: true,
        user: data.user
      };
    } catch (error) {
      console.error('Error in signInWithPhone:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Send OTP for passwordless login (uses WhatsApp system)
   */
  async sendLoginOTP(phone: string): Promise<WhatsAppAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Sending WhatsApp login OTP to:', formattedPhone);

      // Use the Edge Function to check if user exists (bypasses RLS)
      // The Edge Function will handle the profile lookup with service role permissions

      // Use the simplified WhatsApp OTP login system
      const { data, error } = await supabase.functions.invoke('simple-whatsapp-login', {
        body: {
          phone: formattedPhone,
          action: 'send'
        }
      });

      if (error) {
        console.error('Login OTP send error:', error);
        return {
          success: false,
          error: error.message || 'Failed to send WhatsApp OTP'
        };
      }

      if (!data.success) {
        return {
          success: false,
          error: data.error || 'Failed to send WhatsApp OTP'
        };
      }

      console.log('WhatsApp login OTP sent successfully');
      return {
        success: true,
        phone: formattedPhone
      };
    } catch (error) {
      console.error('Error in sendLoginOTP:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Verify login OTP (uses WhatsApp system)
   */
  async verifyLoginOTP(phone: string, otp: string): Promise<WhatsAppAuthResult> {
    try {
      const formattedPhone = this.formatPhoneNumber(phone);

      console.log('Verifying WhatsApp login OTP for:', formattedPhone);

      // Use the simplified WhatsApp OTP verification system
      const { data, error } = await supabase.functions.invoke('simple-whatsapp-login', {
        body: {
          phone: formattedPhone,
          otp: otp,
          action: 'verify'
        }
      });

      if (error) {
        console.error('Login OTP verification error:', error);
        return {
          success: false,
          error: error.message || 'Failed to verify WhatsApp OTP'
        };
      }

      if (!data.success) {
        return {
          success: false,
          error: data.error || 'Invalid OTP'
        };
      }

      // Handle session URL if provided
      if (data.session_url) {
        console.log('Login OTP verified, session URL created');
        // For now, we'll redirect the user manually or handle session differently
        // The session URL can be used to authenticate the user
      }

      console.log('WhatsApp login OTP verified successfully:', data.user?.id);
      return {
        success: true,
        user: data.user,
        sessionUrl: data.session_url
      };
    } catch (error) {
      console.error('Error in verifyLoginOTP:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get supported country codes
   */
  getSupportedCountryCodes(): Array<{code: string, name: string, flag: string}> {
    return [
      { code: '+91', name: 'India', flag: '🇮🇳' },
      { code: '+1', name: 'United States', flag: '🇺🇸' },
      { code: '+44', name: 'United Kingdom', flag: '🇬🇧' },
      { code: '+971', name: 'UAE', flag: '🇦🇪' },
      { code: '+65', name: 'Singapore', flag: '🇸🇬' }
    ];
  }
}

export const whatsappAuthService = new WhatsAppAuthService();
