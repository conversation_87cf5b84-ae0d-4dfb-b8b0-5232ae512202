import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Mail, CheckCircle, ArrowRight, X } from 'lucide-react';
import Header from '../components/Header';
import { toast } from '@/components/ui/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { validateEmail, sanitizeInput } from '@/utils/security';
import { msg91EmailService } from '@/services/msg91EmailService';

const VerifyEmailPrompt: React.FC = () => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const navigate = useNavigate();
  
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [emailError, setEmailError] = useState('');

  // Get user data from navigation state
  const { user, registrationMethod } = location.state || {};

  const handleSendVerificationEmail = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setEmailError('');

    try {
      const sanitizedEmail = sanitizeInput(email);
      
      // Send verification email using MSG91
      const result = await msg91EmailService.sendVerificationEmail(
        sanitizedEmail,
        user?.full_name || 'User',
        `${window.location.origin}/verify-email-token?email=${encodeURIComponent(sanitizedEmail)}`,
        false
      );

      if (result.success) {
        setEmailSent(true);
        toast({
          title: "Verification email sent!",
          description: `We've sent a verification link to ${sanitizedEmail}`,
        });
      } else {
        setEmailError(result.error || 'Failed to send verification email');
        toast({
          title: "Failed to send email",
          description: result.error || 'Please try again.',
          variant: "destructive",
        });
      }
    } catch (error) {
      setEmailError('An unexpected error occurred');
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkipForNow = () => {
    toast({
      title: "Registration complete!",
      description: "You can verify your email later from your profile settings.",
    });
    navigate('/dashboard');
  };

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900">
      <Header />
      
      <div className="pt-24 pb-16">
        <div className="container mx-auto px-4">
          <div className={`${isMobile ? 'max-w-sm' : 'max-w-md'} mx-auto backdrop-blur-sm bg-white/10 rounded-xl shadow-xl overflow-hidden border border-white/20`}>
            <div className="p-8">
              {!emailSent ? (
                <>
                  {/* Success Icon */}
                  <div className="text-center mb-6">
                    <div className="w-20 h-20 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="h-10 w-10 text-green-400" />
                    </div>
                    <h1 className="text-2xl font-bold text-white mb-2">
                      WhatsApp Verified! 🎉
                    </h1>
                    <p className="text-gray-300">
                      Your phone number has been successfully verified
                    </p>
                  </div>

                  {/* Email Verification Prompt */}
                  <div className="bg-indigo-500/20 border border-indigo-500/30 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <Mail className="h-5 w-5 text-indigo-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="text-indigo-300 font-medium mb-1">
                          Verify Your Email
                        </h3>
                        <p className="text-gray-300 text-sm">
                          Email verification is required for booking confirmations, special offers, and important updates.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Email Input Form */}
                  <form onSubmit={handleSendVerificationEmail} className="space-y-4">
                    <div className="group">
                      <label htmlFor="email" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-indigo-light">
                        Email Address
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-indigo-light" />
                        </div>
                        <input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => {
                            setEmail(e.target.value);
                            setEmailError('');
                          }}
                          className={`pl-10 w-full p-3 border bg-navy-light/50 ${emailError ? 'border-red-500' : 'border-indigo/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-light transition-all duration-300`}
                          placeholder="Enter your email address"
                          required
                          maxLength={254}
                        />
                      </div>
                      {emailError && <p className="text-red-400 text-xs mt-1">{emailError}</p>}
                    </div>

                    <div className="flex gap-3">
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="flex-1 py-3 px-4 bg-gradient-to-r from-indigo to-indigo-dark text-white rounded-md hover:from-indigo-dark hover:to-indigo transition-all font-semibold flex justify-center items-center transform hover:scale-[1.02] shadow-lg"
                      >
                        {isLoading ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Sending...
                          </span>
                        ) : (
                          <span className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Verify Now
                          </span>
                        )}
                      </button>
                    </div>
                  </form>

                  {/* Skip Option */}
                  <div className="mt-6 text-center">
                    <button
                      onClick={handleSkipForNow}
                      className="text-gray-400 hover:text-white text-sm transition-colors duration-300 flex items-center justify-center gap-1 mx-auto"
                    >
                      Skip for now
                      <ArrowRight className="h-4 w-4" />
                    </button>
                    <p className="text-gray-500 text-xs mt-2">
                      You can verify your email later from profile settings
                    </p>
                  </div>
                </>
              ) : (
                <>
                  {/* Email Sent Success */}
                  <div className="text-center">
                    <div className="w-20 h-20 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Mail className="h-10 w-10 text-green-400" />
                    </div>
                    <h1 className="text-2xl font-bold text-white mb-2">
                      Verification Email Sent! 📧
                    </h1>
                    <p className="text-gray-300 mb-6">
                      We've sent a verification link to <span className="text-indigo-300 font-medium">{email}</span>
                    </p>
                    
                    <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6">
                      <p className="text-green-300 text-sm">
                        ✅ Check your email and click the verification link to complete the process
                      </p>
                    </div>

                    <button
                      onClick={handleGoToDashboard}
                      className="w-full py-3 px-4 bg-gradient-to-r from-indigo to-indigo-dark text-white rounded-md hover:from-indigo-dark hover:to-indigo transition-all font-semibold flex justify-center items-center transform hover:scale-[1.02] shadow-lg"
                    >
                      <span className="flex items-center gap-2">
                        Continue to Dashboard
                        <ArrowRight className="h-4 w-4" />
                      </span>
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyEmailPrompt;
