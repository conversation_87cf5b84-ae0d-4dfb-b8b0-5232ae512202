
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, User, Mail, Lock, Phone, MessageCircle } from 'lucide-react';
import Header from '../components/Header';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useSecureAuth } from '@/hooks/useSecureAuth';
import { validateEmail, validatePhone, validatePassword, sanitizeInput } from '@/utils/security';
import { customAuthService } from '@/services/customAuthService';
import { whatsappAuthService } from '@/services/whatsappAuthService';

const Register: React.FC = () => {
  // Registration method state
  const [registrationMethod, setRegistrationMethod] = useState<'whatsapp' | 'email'>('whatsapp');

  // Common form fields
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Email registration fields
  const [email, setEmail] = useState('');

  // WhatsApp registration fields
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('+91');
  const [otp, setOtp] = useState('');
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otpTimer, setOtpTimer] = useState(0);
  const [isResendingOtp, setIsResendingOtp] = useState(false);

  // Form validation and loading states
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();
  const { signUp } = useAuth();
  const { secureSignUp } = useSecureAuth();

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(300); // 5 minutes
    const timer = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Format timer display
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Validate WhatsApp registration form
  const validateWhatsAppForm = () => {
    const errors: Record<string, string> = {};

    if (!name || name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    } else if (name.trim().length > 100) {
      errors.name = 'Name must be less than 100 characters';
    }

    if (!phone || !validatePhone(countryCode + phone)) {
      errors.phone = 'Please enter a valid phone number';
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0];
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Validate email registration form
  const validateEmailForm = () => {
    const errors: Record<string, string> = {};

    if (!name || name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    } else if (name.trim().length > 100) {
      errors.name = 'Name must be less than 100 characters';
    }

    if (!email || !validateEmail(email)) {
      errors.email = 'Please enter a valid email address';
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0];
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle WhatsApp registration (send OTP)
  const handleWhatsAppRegistration = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateWhatsAppForm()) {
      return;
    }

    setIsLoading(true);

    const sanitizedData = {
      name: sanitizeInput(name),
      phone: sanitizeInput(phone)
    };

    const fullPhone = countryCode + sanitizedData.phone;

    // Send WhatsApp OTP
    const result = await whatsappAuthService.sendWhatsAppOTP({
      phone: fullPhone,
      full_name: sanitizedData.name,
      password: password
    });

    setIsLoading(false);

    if (result.success) {
      setShowOtpInput(true);
      startOtpTimer();
      toast({
        title: "OTP Sent!",
        description: `We've sent a 6-digit OTP to your WhatsApp number ${fullPhone}`,
      });
    } else {
      toast({
        title: "Failed to send OTP",
        description: result.error || "Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle OTP verification
  const handleOtpVerification = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp || otp.length !== 6) {
      setFieldErrors({ otp: 'Please enter a valid 6-digit OTP' });
      return;
    }

    setIsLoading(true);

    const fullPhone = countryCode + sanitizeInput(phone);

    const result = await whatsappAuthService.verifyWhatsAppOTP({
      phone: fullPhone,
      otp: otp
    });

    setIsLoading(false);

    if (result.success) {
      toast({
        title: "Registration successful!",
        description: "Your WhatsApp number has been verified. Welcome to Grid2Play!",
      });

      // Navigate to email verification prompt
      navigate('/verify-email-prompt', {
        state: {
          user: result.user,
          registrationMethod: 'whatsapp'
        }
      });
    } else {
      toast({
        title: "OTP verification failed",
        description: result.error || "Please check your OTP and try again.",
        variant: "destructive",
      });
    }
  };

  // Handle email registration
  const handleEmailRegistration = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmailForm()) {
      return;
    }

    setIsLoading(true);

    const sanitizedData = {
      name: sanitizeInput(name),
      email: sanitizeInput(email)
    };

    // Use custom auth service with MSG91 email integration
    const { error } = await customAuthService.signUpWithCustomEmail({
      email: sanitizedData.email,
      password: password,
      name: sanitizedData.name,
      phone: undefined
    });

    setIsLoading(false);

    if (!error) {
      // Store email in localStorage for verification page
      localStorage.setItem('pendingVerificationEmail', sanitizedData.email);

      toast({
        title: "Registration successful",
        description: "Please check your email to verify your account. We've sent you a beautiful verification email!",
      });
      // Redirect to verify-email page with email in state
      navigate('/verify-email', { state: { email: sanitizedData.email } });
    } else {
      toast({
        title: "Registration failed",
        description: error.message || "Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleGoogleSignUp = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/`
        }
      });

      if (error) {
        toast({
          title: "Google Sign-Up failed",
          description: "Unable to connect with Google. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Google Sign-Up failed",
        description: "Unable to connect with Google. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-navy-dark to-indigo/30 animate-gradient-x">
      <Header />

      <div className="pt-24 pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto backdrop-blur-sm bg-white/10 rounded-xl shadow-xl overflow-hidden border border-white/20 hover:shadow-2xl transition-all duration-500 hover:-translate-y-1">
            <div className="p-8">
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-indigo-light bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-indigo-light" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <line x1="19" y1="8" x2="19" y2="14"></line>
                    <line x1="22" y1="11" x2="16" y2="11"></line>
                  </svg>
                </div>
                <h1 className="text-3xl font-bold text-white">Create Account</h1>
                <p className="text-gray-300 mt-2">Join Grid2Play today</p>
              </div>

              {/* Registration Method Tabs */}
              <div className="flex mb-6 bg-navy-light/30 rounded-lg p-1">
                <button
                  type="button"
                  onClick={() => setRegistrationMethod('whatsapp')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-300 flex items-center justify-center gap-2 ${
                    registrationMethod === 'whatsapp'
                      ? 'bg-green-600 text-white shadow-lg'
                      : 'text-gray-300 hover:text-white hover:bg-navy-light/50'
                  }`}
                >
                  <MessageCircle className="h-4 w-4" />
                  WhatsApp
                </button>
                <button
                  type="button"
                  onClick={() => setRegistrationMethod('email')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-300 flex items-center justify-center gap-2 ${
                    registrationMethod === 'email'
                      ? 'bg-indigo text-white shadow-lg'
                      : 'text-gray-300 hover:text-white hover:bg-navy-light/50'
                  }`}
                >
                  <Mail className="h-4 w-4" />
                  Email
                </button>
              </div>

              {/* WhatsApp Registration Form */}
              {registrationMethod === 'whatsapp' && (
                <form onSubmit={showOtpInput ? handleOtpVerification : handleWhatsAppRegistration} className="space-y-6">
                  {!showOtpInput ? (
                    <>
                      {/* Full Name Field */}
                      <div className="group">
                        <label htmlFor="whatsapp-name" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-green-400">
                          Full Name
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <User className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-green-400" />
                          </div>
                          <input
                            id="whatsapp-name"
                            type="text"
                            value={name}
                            onChange={(e) => {
                              setName(e.target.value);
                              setFieldErrors(prev => ({ ...prev, name: '' }));
                            }}
                            className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.name ? 'border-red-500' : 'border-green-500/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-300`}
                            placeholder="Enter your full name"
                            required
                            maxLength={100}
                          />
                        </div>
                        {fieldErrors.name && <p className="text-red-400 text-xs mt-1">{fieldErrors.name}</p>}
                      </div>

                      {/* Phone Number Field with Country Code */}
                      <div className="group">
                        <label htmlFor="whatsapp-phone" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-green-400">
                          WhatsApp Number
                        </label>
                        <div className="flex gap-2">
                          <select
                            value={countryCode}
                            onChange={(e) => setCountryCode(e.target.value)}
                            className="w-20 p-3 border bg-navy-light/50 border-green-500/30 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-300"
                          >
                            <option value="+91">🇮🇳 +91</option>
                            <option value="+1">🇺🇸 +1</option>
                            <option value="+44">🇬🇧 +44</option>
                            <option value="+971">🇦🇪 +971</option>
                            <option value="+65">🇸🇬 +65</option>
                          </select>
                          <div className="relative flex-1">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <MessageCircle className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-green-400" />
                            </div>
                            <input
                              id="whatsapp-phone"
                              type="tel"
                              value={phone}
                              onChange={(e) => {
                                setPhone(e.target.value);
                                setFieldErrors(prev => ({ ...prev, phone: '' }));
                              }}
                              className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.phone ? 'border-red-500' : 'border-green-500/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-300`}
                              placeholder="Enter your WhatsApp number"
                              required
                              maxLength={15}
                            />
                          </div>
                        </div>
                        {fieldErrors.phone && <p className="text-red-400 text-xs mt-1">{fieldErrors.phone}</p>}
                        <p className="text-green-400 text-xs mt-1">📱 We'll send a 6-digit OTP to this WhatsApp number</p>
                      </div>
                    </>
                  ) : (
                    /* OTP Input Field */
                    <div className="group">
                      <label htmlFor="otp" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-green-400">
                        Enter OTP
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MessageCircle className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-green-400" />
                        </div>
                        <input
                          id="otp"
                          type="text"
                          value={otp}
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                            setOtp(value);
                            setFieldErrors(prev => ({ ...prev, otp: '' }));
                          }}
                          className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.otp ? 'border-red-500' : 'border-green-500/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-300 text-center text-2xl tracking-widest`}
                          placeholder="000000"
                          required
                          maxLength={6}
                        />
                      </div>
                      {fieldErrors.otp && <p className="text-red-400 text-xs mt-1">{fieldErrors.otp}</p>}
                      <div className="flex justify-between items-center mt-2">
                        <p className="text-green-400 text-xs">
                          📱 OTP sent to {countryCode}{phone}
                        </p>
                        {otpTimer > 0 ? (
                          <p className="text-gray-400 text-xs">
                            Resend in {formatTimer(otpTimer)}
                          </p>
                        ) : (
                          <button
                            type="button"
                            onClick={() => {
                              setShowOtpInput(false);
                              setOtp('');
                            }}
                            className="text-green-400 text-xs hover:text-green-300 transition-colors"
                          >
                            Change Number
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {!showOtpInput && (
                    <>
                      {/* Password Field */}
                      <div className="group">
                        <label htmlFor="whatsapp-password" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-green-400">
                          Password
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Lock className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-green-400" />
                          </div>
                          <input
                            id="whatsapp-password"
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => {
                              setPassword(e.target.value);
                              setFieldErrors(prev => ({ ...prev, password: '' }));
                            }}
                            className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.password ? 'border-red-500' : 'border-green-500/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-300`}
                            placeholder="Create a password"
                            required
                            maxLength={128}
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="text-gray-400 hover:text-green-400 focus:outline-none transition-colors duration-300"
                            >
                              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                            </button>
                          </div>
                        </div>
                        {fieldErrors.password && <p className="text-red-400 text-xs mt-1">{fieldErrors.password}</p>}
                      </div>

                      {/* Confirm Password Field */}
                      <div className="group">
                        <label htmlFor="whatsapp-confirmPassword" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-green-400">
                          Confirm Password
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <Lock className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-green-400" />
                          </div>
                          <input
                            id="whatsapp-confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            value={confirmPassword}
                            onChange={(e) => {
                              setConfirmPassword(e.target.value);
                              setFieldErrors(prev => ({ ...prev, confirmPassword: '' }));
                            }}
                            className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.confirmPassword ? 'border-red-500' : 'border-green-500/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-300`}
                            placeholder="Confirm your password"
                            required
                            maxLength={128}
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button
                              type="button"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              className="text-gray-400 hover:text-green-400 focus:outline-none transition-colors duration-300"
                            >
                              {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                            </button>
                          </div>
                        </div>
                        {fieldErrors.confirmPassword && <p className="text-red-400 text-xs mt-1">{fieldErrors.confirmPassword}</p>}
                      </div>
                    </>
                  )}

                  {/* Submit Button */}
                  <div>
                    <button
                      type="submit"
                      className={`w-full py-3 px-4 bg-gradient-to-r ${
                        showOtpInput
                          ? 'from-green-600 to-green-700 hover:from-green-700 hover:to-green-800'
                          : 'from-green-600 to-green-700 hover:from-green-700 hover:to-green-800'
                      } text-white rounded-md transition-all font-semibold flex justify-center items-center transform hover:scale-[1.02] shadow-lg relative overflow-hidden group`}
                      disabled={isLoading}
                    >
                      <span className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      {isLoading ? (
                        <span className="flex items-center z-10">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {showOtpInput ? 'Verifying OTP...' : 'Sending OTP...'}
                        </span>
                      ) : (
                        <span className="z-10 flex items-center gap-2">
                          <MessageCircle className="h-4 w-4" />
                          {showOtpInput ? 'Verify OTP' : 'Send WhatsApp OTP'}
                        </span>
                      )}
                    </button>
                  </div>
                </form>
              )}

              {/* Email Registration Form */}
              {registrationMethod === 'email' && (
                <form onSubmit={handleEmailRegistration} className="space-y-6">
                  {/* Full Name Field */}
                  <div className="group">
                    <label htmlFor="email-name" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-indigo-light">
                      Full Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-indigo-light" />
                      </div>
                      <input
                        id="email-name"
                        type="text"
                        value={name}
                        onChange={(e) => {
                          setName(e.target.value);
                          setFieldErrors(prev => ({ ...prev, name: '' }));
                        }}
                        className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.name ? 'border-red-500' : 'border-indigo/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-light transition-all duration-300`}
                        placeholder="Enter your full name"
                        required
                        maxLength={100}
                      />
                    </div>
                    {fieldErrors.name && <p className="text-red-400 text-xs mt-1">{fieldErrors.name}</p>}
                  </div>

                  {/* Email Field */}
                  <div className="group">
                    <label htmlFor="email-address" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-indigo-light">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-indigo-light" />
                      </div>
                      <input
                        id="email-address"
                        type="email"
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value);
                          setFieldErrors(prev => ({ ...prev, email: '' }));
                        }}
                        className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.email ? 'border-red-500' : 'border-indigo/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-light transition-all duration-300`}
                        placeholder="Enter your email"
                        required
                        maxLength={254}
                      />
                    </div>
                    {fieldErrors.email && <p className="text-red-400 text-xs mt-1">{fieldErrors.email}</p>}
                  </div>

                  {/* Password Field */}
                  <div className="group">
                    <label htmlFor="email-password" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-indigo-light">
                      Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-indigo-light" />
                      </div>
                      <input
                        id="email-password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => {
                          setPassword(e.target.value);
                          setFieldErrors(prev => ({ ...prev, password: '' }));
                        }}
                        className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.password ? 'border-red-500' : 'border-indigo/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-light transition-all duration-300`}
                        placeholder="Create a password"
                        required
                        maxLength={128}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="text-gray-400 hover:text-indigo-light focus:outline-none transition-colors duration-300"
                        >
                          {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                        </button>
                      </div>
                    </div>
                    {fieldErrors.password && <p className="text-red-400 text-xs mt-1">{fieldErrors.password}</p>}
                  </div>

                  {/* Confirm Password Field */}
                  <div className="group">
                    <label htmlFor="email-confirmPassword" className="block text-sm font-medium text-gray-200 mb-1 transition-all duration-300 group-focus-within:text-indigo-light">
                      Confirm Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-gray-400 transition-all duration-300 group-focus-within:text-indigo-light" />
                      </div>
                      <input
                        id="email-confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={confirmPassword}
                        onChange={(e) => {
                          setConfirmPassword(e.target.value);
                          setFieldErrors(prev => ({ ...prev, confirmPassword: '' }));
                        }}
                        className={`pl-10 w-full p-3 border bg-navy-light/50 ${fieldErrors.confirmPassword ? 'border-red-500' : 'border-indigo/30'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-light transition-all duration-300`}
                        placeholder="Confirm your password"
                        required
                        maxLength={128}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="text-gray-400 hover:text-indigo-light focus:outline-none transition-colors duration-300"
                        >
                          {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                        </button>
                      </div>
                    </div>
                    {fieldErrors.confirmPassword && <p className="text-red-400 text-xs mt-1">{fieldErrors.confirmPassword}</p>}
                  </div>

                  {/* Submit Button */}
                  <div>
                    <button
                      type="submit"
                      className="w-full py-3 px-4 bg-gradient-to-r from-indigo to-indigo-dark text-white rounded-md hover:from-indigo-dark hover:to-indigo transition-all font-semibold flex justify-center items-center transform hover:scale-[1.02] shadow-lg relative overflow-hidden group"
                      disabled={isLoading}
                    >
                      <span className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      {isLoading ? (
                        <span className="flex items-center z-10">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Creating Account...
                        </span>
                      ) : (
                        <span className="z-10 flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          Sign Up with Email
                        </span>
                      )}
                    </button>
                  </div>
                </form>
              )}

              <div className="my-6 text-center">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-600"></div>
                  </div>
                  <div className="relative flex justify-center">
                    <span className="px-2 bg-transparent text-gray-400 text-sm">or continue with</span>
                  </div>
                </div>
                <button
                  onClick={handleGoogleSignUp}
                  className="w-full mt-6 py-3 px-4 bg-white/90 text-black font-medium rounded-md hover:bg-white transition-all flex items-center justify-center gap-2 transform hover:scale-[1.02] shadow-lg"
                >
                  <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" className="w-5 h-5" />
                  Google
                </button>
              </div>

              <div className="mt-6 text-center">
                <p className="text-gray-300">
                  Already have an account?{' '}
                  <Link to="/login" className="text-indigo-light hover:text-white font-medium transition-colors duration-300">
                    Sign in
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <footer className="bg-navy-dark/50 backdrop-blur-sm py-6">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-400">&copy; 2025 Grid2Play. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Register;
