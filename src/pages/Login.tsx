
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react';
import Header from '../components/Header';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { useSecureAuth } from '@/hooks/useSecureAuth';
import { validateEmail, sanitizeInput } from '@/utils/security';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [isResetting, setIsResetting] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const isMobile = useIsMobile();

  const { signIn } = useAuth();
  const { secureSignIn, isLoading } = useSecureAuth();

  const validateForm = () => {
    let isValid = true;
    setEmailError('');
    setPasswordError('');

    if (!email || !validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    if (!password || password.length < 6) {
      setPasswordError('Password must be at least 6 characters long');
      isValid = false;
    }

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const sanitizedEmail = sanitizeInput(email);
    const { error } = await secureSignIn(sanitizedEmail, password);

    if (!error) {
      toast({
        title: "Login successful",
        description: "Welcome back to Grid2Play!",
      });
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!resetEmail || !validateEmail(resetEmail)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    setIsResetting(true);

    try {
      const sanitizedEmail = sanitizeInput(resetEmail);
      const { error } = await supabase.auth.resetPasswordForEmail(sanitizedEmail, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        console.error('Password reset error:', error);
        // Generic message to prevent email enumeration
        toast({
          title: "Reset request processed",
          description: "If an account with this email exists, you will receive password reset instructions.",
        });
      } else {
        toast({
          title: "Reset email sent",
          description: "Please check your email for password reset instructions. The link will redirect you to set a new password.",
        });
        setShowForgotPassword(false);
        setResetEmail('');
      }
    } catch (error: any) {
      console.error('Unexpected error:', error);
      toast({
        title: "Reset request processed",
        description: "If an account with this email exists, you will receive password reset instructions.",
      });
    } finally {
      setIsResetting(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/`
        }
      });

      if (error) {
        toast({
          title: "Google Sign-In failed",
          description: "Unable to connect with Google. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Google Sign-In failed",
        description: "Unable to connect with Google. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (showForgotPassword) {
    return (
      <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden">
        {/* Floating dark green SVG accent, hidden on mobile */}
        <div className="hidden sm:block absolute -top-32 -left-32 w-[400px] h-[400px] pointer-events-none opacity-20 animate-float z-0">
          <svg viewBox="0 0 400 400" fill="none">
            <ellipse cx="200" cy="200" rx="200" ry="200" fill="#1E3B2C" />
          </svg>
        </div>
        <Header />

        <div className="pt-8 pb-4 sm:pt-24 sm:pb-16 relative z-10 w-full flex-1 flex flex-col justify-center items-center sm:block" style={{ minHeight: 'calc(100vh - 64px)' }}>
          <div className="container mx-auto px-2 sm:px-4">
            <div className="w-full mx-0 sm:max-w-md sm:mx-auto bg-black/80 border-2 border-[#1E3B2C]/60 shadow-xl sm:shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden animate-fade-in backdrop-blur-lg">
              <div className="p-4 sm:p-8">
                <div className="text-center mb-6 sm:mb-8">
                  <div className="w-12 h-12 sm:w-20 sm:h-20 bg-[#1E3B2C]/30 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                    <Mail className="h-6 w-6 sm:h-10 sm:w-10 text-[#2E7D32]" />
                  </div>
                  <h1 className="text-xl sm:text-3xl font-extrabold text-[#2E7D32] drop-shadow">Reset Password</h1>
                  <p className="text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">Enter your email to receive reset instructions</p>
                </div>

                <form onSubmit={handleForgotPassword} className="space-y-4 sm:space-y-6">
                  <div>
                    <label htmlFor="reset-email" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                      </div>
                      <input
                        id="reset-email"
                        type="email"
                        value={resetEmail}
                        onChange={(e) => setResetEmail(e.target.value)}
                        className="pl-10 w-full p-2 sm:p-3 border border-[#1E3B2C]/60 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base"
                        placeholder="Enter your email"
                        required
                        maxLength={254}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <button
                      type="submit"
                      className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-[#1E3B2C] via-[#2E7D32] to-[#1E3B2C] text-white rounded-md font-bold shadow-md sm:shadow-lg hover:from-[#2E7D32] hover:to-[#1E3B2C] hover:shadow-[#2E7D32]/40 transition-all flex justify-center items-center transform hover:scale-[1.03] focus:outline-none focus:ring-2 focus:ring-[#2E7D32] text-sm sm:text-lg"
                      disabled={isResetting}
                    >
                      {isResetting ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Sending...
                        </span>
                      ) : (
                        'Send Reset Email'
                      )}
                    </button>

                    <button
                      type="button"
                      onClick={() => setShowForgotPassword(false)}
                      className="w-full py-2 sm:py-3 px-4 border border-[#1E3B2C]/60 text-[#2E7D32] rounded-md font-medium hover:bg-[#1E3B2C]/20 transition-all text-sm sm:text-base"
                    >
                      Back to Login
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-black via-[#1E3B2C] to-black overflow-hidden">
      {/* Floating dark green SVG accent, hidden on mobile */}
      <div className="hidden sm:block absolute -top-32 -left-32 w-[400px] h-[400px] pointer-events-none opacity-20 animate-float z-0">
        <svg viewBox="0 0 400 400" fill="none">
          <ellipse cx="200" cy="200" rx="200" ry="200" fill="#1E3B2C" />
        </svg>
      </div>
      <Header />

      <div className="pt-8 pb-4 sm:pt-24 sm:pb-16 relative z-10 w-full flex-1 flex flex-col justify-center items-center sm:block" style={{ minHeight: 'calc(100vh - 64px)' }}>
        <div className="container mx-auto px-2 sm:px-4">
          <div className="w-full mx-0 sm:max-w-md sm:mx-auto bg-black/80 border-2 border-[#1E3B2C]/60 shadow-xl sm:shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden animate-fade-in backdrop-blur-lg">
            <div className="p-4 sm:p-8">
              <div className="text-center mb-6 sm:mb-8">
                <div className="w-12 h-12 sm:w-20 sm:h-20 bg-[#1E3B2C]/30 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 sm:h-10 sm:w-10 text-[#2E7D32]" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 2a10 10 0 1 0 0 20 10 10 0 1 0 0-20z"></path>
                    <path d="M12 8v8"></path>
                    <path d="M8 12h8"></path>
                  </svg>
                </div>
                <h1 className="text-xl sm:text-3xl font-extrabold text-[#2E7D32] drop-shadow">Welcome Back</h1>
                <p className="text-gray-300 mt-1 sm:mt-2 text-sm sm:text-base">Sign in to continue with Grid2Play</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                <div>
                  <label htmlFor="email" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                    </div>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        setEmailError('');
                      }}
                      className={`pl-10 w-full p-2 sm:p-3 border ${emailError ? 'border-red-500' : 'border-[#1E3B2C]/60'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base`}
                      placeholder="Enter your email"
                      required
                      maxLength={254}
                    />
                  </div>
                  {emailError && <p className="text-red-400 text-xs mt-1">{emailError}</p>}
                </div>

                <div>
                  <label htmlFor="password" className="block text-xs sm:text-sm font-medium text-[#2E7D32] mb-1">
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-4 w-4 sm:h-5 sm:w-5 text-[#2E7D32]" />
                    </div>
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value);
                        setPasswordError('');
                      }}
                      className={`pl-10 w-full p-2 sm:p-3 border ${passwordError ? 'border-red-500' : 'border-[#1E3B2C]/60'} bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-[#2E7D32] transition-all text-sm sm:text-base`}
                      placeholder="Enter your password"
                      required
                      maxLength={128}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-[#2E7D32] hover:text-white focus:outline-none"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" /> : <Eye className="h-4 w-4 sm:h-5 sm:w-5" />}
                      </button>
                    </div>
                  </div>
                  {passwordError && <p className="text-red-400 text-xs mt-1">{passwordError}</p>}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      className="h-4 w-4 text-[#2E7D32] focus:ring-[#2E7D32] border-[#1E3B2C]/60 rounded bg-black/70"
                    />
                    <label htmlFor="remember-me" className="ml-2 block text-xs sm:text-sm text-gray-200">
                      Remember me
                    </label>
                  </div>

                  <div className="text-xs sm:text-sm">
                    <button
                      type="button"
                      onClick={() => setShowForgotPassword(true)}
                      className="font-medium text-[#2E7D32] hover:text-white transition-colors"
                    >
                      Forgot password?
                    </button>
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    className="w-full py-2 sm:py-3 px-4 bg-gradient-to-r from-[#1E3B2C] via-[#2E7D32] to-[#1E3B2C] text-white rounded-md font-bold shadow-md sm:shadow-lg hover:from-[#2E7D32] hover:to-[#1E3B2C] hover:shadow-[#2E7D32]/40 transition-all flex justify-center items-center transform hover:scale-[1.03] focus:outline-none focus:ring-2 focus:ring-[#2E7D32] text-sm sm:text-lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing In...
                      </span>
                    ) : (
                      'Sign In'
                    )}
                  </button>
                </div>
              </form>

              <div className="my-4 sm:my-6 text-center">
                <p className="text-gray-400 mb-2 text-xs sm:text-base">or</p>
                <button
                  onClick={handleGoogleSignIn}
                  className="w-full py-2 sm:py-3 px-4 bg-white/90 text-black font-medium rounded-md hover:bg-[#1E3B2C]/90 hover:text-white transition-all flex items-center justify-center gap-2 transform hover:scale-[1.03] shadow-md sm:shadow-lg text-sm sm:text-base"
                >
                  <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" className="w-5 h-5" />
                  Continue with Google
                </button>
              </div>

              <div className="mt-4 sm:mt-6 text-center">
                <p className="text-gray-300 text-xs sm:text-base">
                  Don't have an account?{' '}
                  <Link to="/register" className="text-[#2E7D32] hover:text-white font-medium transition-colors">
                    Sign up
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
