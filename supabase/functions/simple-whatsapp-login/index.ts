import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone, otp, action } = await req.json()

    // Validate input
    if (!phone) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number is required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Format phone number
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (action === 'send') {
      // Send OTP for login
      console.log('Sending WhatsApp login OTP to:', formattedPhone)

      // Check if user exists with verified phone
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified, full_name, email')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)

      if (profileError) {
        console.error('Profile lookup error:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!profiles || profiles.length === 0) {
        console.error('No verified profile found for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found or not verified. Please register first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use the first (and should be only) profile
      const profile = profiles[0]

      console.log('Found verified profile for login:', profile.id)

      // Use existing send-whatsapp-otp function
      const { data, error } = await supabaseAdmin.functions.invoke('send-whatsapp-otp', {
        body: {
          phone: formattedPhone,
          full_name: profile.full_name || 'User',
          password: 'temp_login_password', // Not used for login OTP
          isLogin: true
        }
      })

      if (error || !data?.success) {
        console.error('Failed to send WhatsApp OTP:', error || data?.error)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send WhatsApp OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'WhatsApp OTP sent successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'verify') {
      // Verify OTP and login
      if (!otp) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP is required'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Verifying WhatsApp login OTP for:', formattedPhone)

      // Validate OTP using database function
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_whatsapp_otp', { 
          phone_number: formattedPhone, 
          otp_input: otp 
        })

      if (validationError) {
        console.error('OTP validation error:', validationError)
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'OTP validation failed' 
          }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      const validation = validationResult[0]
      if (!validation || !validation.is_valid) {
        return new Response(
          JSON.stringify({ 
            success: false,
            error: validation?.error_message || 'Invalid OTP' 
          }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // OTP is valid, get user profile
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)

      if (profileError) {
        console.error('Profile lookup error after OTP validation:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!profiles || profiles.length === 0) {
        console.error('No verified profile found for phone after OTP validation:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use the first (and should be only) profile
      const profile = profiles[0]

      // Get user from auth.users
      const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(profile.id)

      if (authError || !authUser.user) {
        console.error('Auth user lookup error:', authError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Authentication failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Create session using the user's email (temp or real)
      const userEmail = authUser.user.email
      
      if (!userEmail) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User authentication method not found'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Generate magic link for session
      const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'magiclink',
        email: userEmail,
        options: {
          redirectTo: `${req.headers.get('origin') || 'https://grid2play.com'}/dashboard`
        }
      })

      if (sessionError) {
        console.error('Error generating session:', sessionError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to create session'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Clean up OTP data
      await supabaseAdmin
        .from('whatsapp_otps')
        .delete()
        .eq('phone', formattedPhone)

      console.log('WhatsApp OTP login successful for user:', profile.id)

      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: profile.id,
            phone: profile.phone,
            full_name: profile.full_name,
            email: profile.email?.includes('@temp.grid2play.com') ? null : profile.email,
            phone_verified: profile.phone_verified,
            email_verified: profile.email_verified
          },
          session_url: sessionData?.properties?.action_link || null
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid action. Use "send" or "verify"'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Simple WhatsApp login error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
