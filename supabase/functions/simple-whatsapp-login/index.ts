import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone, otp, action } = await req.json()

    // Validate input
    if (!phone) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number is required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!action) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Action is required (send or verify)'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Format phone number
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (action === 'send') {
      // Send OTP for login
      console.log('Sending WhatsApp login OTP to:', formattedPhone)

      // Check if user exists with verified phone
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified, full_name, email')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)

      if (profileError) {
        console.error('Profile lookup error:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!profiles || profiles.length === 0) {
        console.error('No verified profile found for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found or not verified. Please register first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use the first (and should be only) profile
      const profile = profiles[0]

      console.log('Found verified profile for login:', profile.id)

      // Generate OTP for login (bypass registration function)
      const otp = Math.floor(100000 + Math.random() * 900000).toString()
      const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

      // Store OTP in whatsapp_otps table
      const { error: otpError } = await supabaseAdmin
        .from('whatsapp_otps')
        .upsert({
          phone: formattedPhone,
          otp_hash: otp, // In production, you should hash this
          expires_at: otpExpiresAt.toISOString(),
          attempts: 0,
          verified: false,
          created_at: new Date().toISOString()
        }, {
          onConflict: 'phone'
        })

      if (otpError) {
        console.error('Error storing login OTP:', otpError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to generate OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Send WhatsApp message directly
      const msg91AuthKey = Deno.env.get('MSG91_AUTH_KEY')
      if (!msg91AuthKey) {
        console.error('MSG91_AUTH_KEY not found')
        return new Response(
          JSON.stringify({
            success: false,
            error: 'WhatsApp service configuration error'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Send WhatsApp OTP using MSG91
      const whatsappPayload = {
        integrated_number: "918448609110", // Your WhatsApp business number
        content_type: "template",
        payload: {
          to: formattedPhone,
          type: "template",
          template: {
            name: "grid2play_otp",
            language: {
              code: "en"
            },
            components: [
              {
                type: "button",
                sub_type: "url",
                index: "0",
                parameters: [
                  {
                    type: "text",
                    text: otp
                  }
                ]
              }
            ]
          }
        }
      }

      const whatsappResponse = await fetch('https://control.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'authkey': msg91AuthKey
        },
        body: JSON.stringify(whatsappPayload)
      })

      const whatsappResult = await whatsappResponse.json()

      if (!whatsappResponse.ok) {
        console.error('WhatsApp API error:', whatsappResult)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send WhatsApp OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('WhatsApp login OTP sent successfully:', whatsappResult)

      return new Response(
        JSON.stringify({
          success: true,
          message: 'WhatsApp OTP sent successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'verify') {
      // Verify OTP and login
      if (!otp) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP is required'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Verifying WhatsApp login OTP for:', formattedPhone)

      // Validate OTP directly from whatsapp_otps table
      const { data: otpRecord, error: otpError } = await supabaseAdmin
        .from('whatsapp_otps')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('otp_hash', otp) // In production, you should hash the input OTP and compare
        .eq('verified', false)
        .gt('expires_at', new Date().toISOString())
        .single()

      if (otpError || !otpRecord) {
        console.error('OTP validation error:', otpError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid or expired OTP'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Mark OTP as verified
      await supabaseAdmin
        .from('whatsapp_otps')
        .update({ verified: true })
        .eq('phone', formattedPhone)
        .eq('otp_hash', otp)

      // OTP is valid, get user profile
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)

      if (profileError) {
        console.error('Profile lookup error after OTP validation:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!profiles || profiles.length === 0) {
        console.error('No verified profile found for phone after OTP validation:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use the first (and should be only) profile
      const profile = profiles[0]

      // Get user from auth.users
      const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(profile.id)

      if (authError || !authUser.user) {
        console.error('Auth user lookup error:', authError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Authentication failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Create session using the user's email (temp or real)
      const userEmail = authUser.user.email
      
      if (!userEmail) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User authentication method not found'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Generate magic link for session
      const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'magiclink',
        email: userEmail,
        options: {
          redirectTo: `${req.headers.get('origin') || 'https://grid2play.com'}/dashboard`
        }
      })

      if (sessionError) {
        console.error('Error generating session:', sessionError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to create session'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Clean up OTP data
      await supabaseAdmin
        .from('whatsapp_otps')
        .delete()
        .eq('phone', formattedPhone)

      console.log('WhatsApp OTP login successful for user:', profile.id)

      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: profile.id,
            phone: profile.phone,
            full_name: profile.full_name,
            email: profile.email?.includes('@temp.grid2play.com') ? null : profile.email,
            phone_verified: profile.phone_verified,
            email_verified: profile.email_verified
          },
          session_url: sessionData?.properties?.action_link || null
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid action. Use "send" or "verify"'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Simple WhatsApp login error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
