import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone, otp, action } = await req.json()

    console.log('WhatsApp Login OTP request:', { phone, action, hasOtp: !!otp })

    // Validate input
    if (!phone) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number is required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!action || (action !== 'send' && action !== 'verify')) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Action must be "send" or "verify"'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Format phone number
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (action === 'send') {
      console.log('Sending WhatsApp login OTP to:', formattedPhone)

      // Check if user exists with verified phone
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified, full_name, email')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)

      if (profileError) {
        console.error('Profile lookup error:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!profiles || profiles.length === 0) {
        console.error('No verified profile found for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found or not verified. Please register first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const profile = profiles[0]
      console.log('Found verified profile for login:', profile.id)

      // Generate OTP for login
      const loginOtp = Math.floor(100000 + Math.random() * 900000).toString()
      const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

      // First, delete any existing OTP for this phone
      await supabaseAdmin
        .from('phone_otps')
        .delete()
        .eq('phone', formattedPhone)

      // Store new OTP in phone_otps table
      const { error: otpError } = await supabaseAdmin
        .from('phone_otps')
        .insert({
          phone: formattedPhone,
          otp_hash: loginOtp,
          expires_at: otpExpiresAt.toISOString(),
          attempts: 0,
          verified: false,
          user_id: profile.id
        })

      if (otpError) {
        console.error('Error storing login OTP:', otpError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to generate OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Send WhatsApp OTP using MSG91
      const msg91AuthKey = Deno.env.get('MSG91_AUTH_KEY')
      if (!msg91AuthKey) {
        console.error('MSG91_AUTH_KEY not found')
        return new Response(
          JSON.stringify({
            success: false,
            error: 'WhatsApp service configuration error'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const whatsappPayload = {
        integrated_number: "918448609110",
        content_type: "template",
        payload: {
          to: formattedPhone,
          type: "template",
          template: {
            name: "grid2play_otp",
            language: {
              code: "en"
            },
            components: [
              {
                type: "button",
                sub_type: "url",
                index: "0",
                parameters: [
                  {
                    type: "text",
                    text: loginOtp
                  }
                ]
              }
            ]
          }
        }
      }

      const whatsappResponse = await fetch('https://control.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'authkey': msg91AuthKey
        },
        body: JSON.stringify(whatsappPayload)
      })

      const whatsappResult = await whatsappResponse.json()

      if (!whatsappResponse.ok) {
        console.error('WhatsApp API error:', whatsappResult)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send WhatsApp OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('WhatsApp login OTP sent successfully')

      return new Response(
        JSON.stringify({
          success: true,
          message: 'WhatsApp OTP sent successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'verify') {
      if (!otp) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP is required for verification'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Verifying WhatsApp login OTP for:', formattedPhone)

      // Validate OTP from phone_otps table
      const { data: otpRecord, error: otpError } = await supabaseAdmin
        .from('phone_otps')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('otp_hash', otp)
        .eq('verified', false)
        .gt('expires_at', new Date().toISOString())
        .single()

      if (otpError || !otpRecord) {
        console.error('OTP validation error:', otpError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid or expired OTP'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Mark OTP as verified
      await supabaseAdmin
        .from('phone_otps')
        .update({ verified: true })
        .eq('phone', formattedPhone)
        .eq('otp_hash', otp)

      // Get user profile
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)

      if (profileError || !profiles || profiles.length === 0) {
        console.error('Profile lookup error after OTP validation:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const profile = profiles[0]

      // Get auth user
      const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(profile.id)

      if (authError || !authUser.user || !authUser.user.email) {
        console.error('Auth user lookup error:', authError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Authentication failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Generate session
      const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'magiclink',
        email: authUser.user.email,
        options: {
          redirectTo: `${req.headers.get('origin') || 'https://grid2play.com'}/dashboard`
        }
      })

      if (sessionError) {
        console.error('Error generating session:', sessionError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to create session'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Clean up OTP
      await supabaseAdmin
        .from('phone_otps')
        .delete()
        .eq('phone', formattedPhone)

      console.log('WhatsApp OTP login successful for user:', profile.id)

      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: profile.id,
            phone: profile.phone,
            full_name: profile.full_name,
            email: profile.email?.includes('@temp.grid2play.com') ? null : profile.email,
            phone_verified: profile.phone_verified,
            email_verified: profile.email_verified
          },
          session_url: sessionData?.properties?.action_link || null
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('WhatsApp login OTP error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
