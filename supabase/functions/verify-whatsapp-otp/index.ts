import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { phone, otp } = await req.json()

    if (!phone || !otp) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: phone, otp' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean phone number
    const cleanPhone = phone.replace(/[\s()-]/g, '')

    // Validate OTP format (6 digits)
    if (!/^\d{6}$/.test(otp)) {
      return new Response(
        JSON.stringify({ error: 'Invalid OTP format. Must be 6 digits.' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Verifying WhatsApp OTP for phone:', cleanPhone)

    // Validate OTP using database function
    const { data: validationResult, error: validationError } = await supabaseAdmin
      .rpc('validate_whatsapp_otp', { 
        phone_number: cleanPhone, 
        otp_input: otp 
      })

    if (validationError) {
      console.error('OTP validation error:', validationError)
      return new Response(
        JSON.stringify({ error: 'OTP validation failed' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const validation = validationResult[0]
    if (!validation || !validation.is_valid) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: validation?.error_message || 'Invalid OTP' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // OTP is valid, create Supabase user
    const userData = validation.user_data
    console.log('Creating Supabase user for verified phone:', cleanPhone)

    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      phone: cleanPhone,
      password: userData.password_hash,
      user_metadata: {
        full_name: userData.full_name,
        phone: cleanPhone,
        registration_method: 'whatsapp'
      },
      phone_confirm: true // Mark phone as confirmed
    })

    if (authError) {
      console.error('Error creating Supabase user:', authError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user account',
          details: authError.message 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!authData.user) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'User creation failed - no user data returned' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('User created successfully:', authData.user.id)

    // Create/update profile with verification status
    const { error: profileError } = await supabaseAdmin.from('profiles').upsert({
      id: authData.user.id,
      full_name: userData.full_name,
      phone: cleanPhone,
      email_verified: false,
      phone_verified: true, // Mark phone as verified
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })

    if (profileError) {
      console.error('Error creating profile:', profileError)
      // Don't fail the request if profile creation fails, user is still created
      console.log('User created but profile creation failed - user can still login')
    } else {
      console.log('Profile created successfully with phone_verified: true')
    }

    // Wait a moment for profile creation to complete
    await new Promise(resolve => setTimeout(resolve, 100))

    // Create default user role with better error handling
    console.log('Creating user role for user ID:', authData.user.id)

    // First check if role already exists
    const { data: existingRole, error: checkError } = await supabaseAdmin
      .from('user_roles')
      .select('*')
      .eq('user_id', authData.user.id)
      .eq('role', 'user')
      .maybeSingle()

    if (checkError) {
      console.error('Error checking existing role:', checkError)
    }

    if (existingRole) {
      console.log('User role already exists:', existingRole)
    } else {
      // Try to create the role
      const { data: roleData, error: roleError } = await supabaseAdmin.from('user_roles').insert({
        user_id: authData.user.id,
        role: 'user'
      }).select()

      if (roleError) {
        console.error('Error creating user role:', roleError)
        console.error('Role error details:', JSON.stringify(roleError, null, 2))
        console.error('User ID being used:', authData.user.id)

        // Check if error is due to duplicate role (which is actually OK)
        if (roleError.code === '23505' && roleError.message?.includes('user_roles_user_id_role_key')) {
          console.log('User role already exists (duplicate key) - this is OK, user registration successful')
        } else {
          console.log('User created but role creation failed with unexpected error')
        }
      } else {
        console.log('User role created successfully:', roleData)
      }
    }

    // Clean up pending WhatsApp user data
    const { error: cleanupError } = await supabaseAdmin
      .from('pending_whatsapp_users')
      .delete()
      .eq('phone', cleanPhone)

    if (cleanupError) {
      console.error('Error cleaning up pending data:', cleanupError)
      // Don't fail the request if cleanup fails
    } else {
      console.log('Pending WhatsApp user data cleaned up')
    }

    // Generate session for the user
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'magiclink',
      email: authData.user.email || `${authData.user.id}@temp.grid2play.com`,
      options: {
        redirectTo: `${req.headers.get('origin') || 'https://grid2play.com'}/dashboard`
      }
    })

    if (sessionError) {
      console.error('Error generating session:', sessionError)
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'WhatsApp OTP verified successfully',
        user: {
          id: authData.user.id,
          phone: cleanPhone,
          full_name: userData.full_name,
          phone_verified: true,
          email_verified: false
        },
        session_url: sessionData?.properties?.action_link || null
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in verify-whatsapp-otp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
