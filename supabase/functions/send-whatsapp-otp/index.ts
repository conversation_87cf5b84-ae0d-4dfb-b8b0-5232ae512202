import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 WhatsApp configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const integratedNumber = Deno.env.get('MSG91_INTEGRATED_NUMBER') || '919211433389'

    if (!authKey) {
      console.error('MSG91 configuration missing')
      return new Response(
        JSON.stringify({ error: 'MSG91 configuration not found' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const { phone, full_name, password } = await req.json()

    if (!phone || !full_name || !password) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: phone, full_name, password' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/
    const cleanPhone = phone.replace(/[\s()-]/g, '')
    if (!phoneRegex.test(cleanPhone)) {
      return new Response(
        JSON.stringify({ error: 'Invalid phone number format' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if phone already exists in profiles
    const { data: existingProfile, error: profileCheckError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('phone', cleanPhone)
      .maybeSingle()

    if (profileCheckError) {
      console.error('Error checking existing profile:', profileCheckError)
      return new Response(
        JSON.stringify({ error: 'Database error' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (existingProfile) {
      return new Response(
        JSON.stringify({ error: 'Phone number already registered' }),
        { 
          status: 409, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check rate limiting using database function
    const { data: rateLimitCheck, error: rateLimitError } = await supabaseAdmin
      .rpc('check_otp_rate_limit', { phone_number: cleanPhone })

    if (rateLimitError) {
      console.error('Rate limit check error:', rateLimitError)
      return new Response(
        JSON.stringify({ error: 'Rate limit check failed' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!rateLimitCheck) {
      return new Response(
        JSON.stringify({ error: 'Too many OTP requests. Please try again later.' }),
        { 
          status: 429, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString()
    const otpExpiresAt = new Date(Date.now() + 5 * 60 * 1000) // 5 minutes

    console.log('Generated OTP for phone:', cleanPhone, 'OTP:', otp)

    // Store pending WhatsApp user data
    const { error: pendingError } = await supabaseAdmin
      .from('pending_whatsapp_users')
      .upsert({
        phone: cleanPhone,
        full_name: full_name.trim(),
        password_hash: password, // In production, this should be hashed
        otp_code: otp,
        otp_expires_at: otpExpiresAt.toISOString(),
        attempts: 0,
        last_otp_sent_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'phone'
      })

    if (pendingError) {
      console.error('Error storing pending WhatsApp user:', pendingError)
      return new Response(
        JSON.stringify({ error: 'Failed to store OTP data' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Prepare MSG91 WhatsApp API payload
    const msg91Payload = {
      integrated_number: integratedNumber,
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: "grid2play_otp",
          language: {
            code: "en",
            policy: "deterministic"
          },
          namespace: "b2b10581_1cc2_41dd_9ff1_af56d017433d",
          to_and_components: [
            {
              to: [cleanPhone],
              components: {
                body_1: {
                  type: "text",
                  value: otp
                },
                button_1: {
                  subtype: "url",
                  type: "text",
                  value: "https://grid2play.com/verify-otp"
                }
              }
            }
          ]
        }
      }
    }

    console.log('Sending WhatsApp OTP via MSG91:', { phone: cleanPhone, template: 'grid2play_otp' })

    // Send WhatsApp OTP via MSG91 API
    const response = await fetch('https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'authkey': authKey
      },
      body: JSON.stringify(msg91Payload)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('MSG91 WhatsApp API error:', errorText)
      
      // Clean up pending record if WhatsApp fails
      await supabaseAdmin
        .from('pending_whatsapp_users')
        .delete()
        .eq('phone', cleanPhone)

      return new Response(
        JSON.stringify({ 
          success: false,
          error: `WhatsApp delivery failed: ${response.status} ${response.statusText}`,
          details: errorText 
        }),
        { 
          status: response.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const responseData = await response.json()
    console.log('WhatsApp OTP sent successfully via MSG91:', responseData)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'WhatsApp OTP sent successfully',
        phone: cleanPhone,
        expires_in: 300 // 5 minutes in seconds
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in send-whatsapp-otp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
