import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { phone, otp, newPassword, action } = await req.json()

    console.log('Forgot Password WhatsApp request:', { phone, action, hasOtp: !!otp, hasNewPassword: !!newPassword })

    // Validate input
    if (!phone) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number is required'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!action || !['send-otp', 'verify-otp', 'reset-password'].includes(action)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Action must be "send-otp", "verify-otp", or "reset-password"'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Format phone number
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`

    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    if (action === 'send-otp') {
      console.log('Sending password reset OTP to:', formattedPhone)

      // Check if user exists with verified phone
      const { data: profiles, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified, full_name, email')
        .eq('phone', formattedPhone)

      if (profileError) {
        console.error('Profile lookup error:', profileError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Database error during profile lookup'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!profiles || profiles.length === 0) {
        console.error('No profile found for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found. Please check your phone number.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Find a verified profile (handle both boolean true and string 'true')
      const profile = profiles.find(p => p.phone_verified === true || String(p.phone_verified) === 'true')

      if (!profile) {
        console.error('No verified profile found for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not verified. Please verify your phone number first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }
      console.log('Found verified profile for password reset:', profile.id)

      // Check rate limiting
      const { data: rateLimitCheck, error: rateLimitError } = await supabaseAdmin
        .rpc('check_otp_rate_limit', { phone_number: formattedPhone })

      if (rateLimitError) {
        console.error('Rate limit check error:', rateLimitError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Rate limit check failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!rateLimitCheck) {
        console.error('Rate limit exceeded for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Too many OTP requests. Please wait before requesting another OTP.'
          }),
          {
            status: 429,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Generate OTP for password reset
      const resetOtp = Math.floor(100000 + Math.random() * 900000).toString()
      const otpExpiresAt = new Date(Date.now() + 5 * 60 * 1000) // 5 minutes

      // Store password reset OTP
      const { error: otpError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .upsert({
          phone: formattedPhone,
          full_name: profile.full_name || 'User',
          password_hash: 'password_reset_temp', // Temporary placeholder
          otp_code: resetOtp,
          otp_expires_at: otpExpiresAt.toISOString(),
          attempts: 0,
          purpose: 'password_reset',
          last_otp_sent_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'phone'
        })

      if (otpError) {
        console.error('Error storing password reset OTP:', otpError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to generate password reset OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Send WhatsApp OTP directly using MSG91
      const msg91AuthKey = Deno.env.get('MSG91_AUTH_KEY')
      if (!msg91AuthKey) {
        console.error('MSG91_AUTH_KEY not found')
        return new Response(
          JSON.stringify({
            success: false,
            error: 'WhatsApp service configuration error'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const whatsappPayload = {
        integrated_number: "918448609110",
        content_type: "template",
        payload: {
          to: formattedPhone,
          type: "template",
          template: {
            name: "grid2play_otp",
            language: {
              code: "en"
            },
            components: [
              {
                type: "button",
                sub_type: "url",
                index: "0",
                parameters: [
                  {
                    type: "text",
                    text: resetOtp
                  }
                ]
              }
            ]
          }
        }
      }

      const whatsappResponse = await fetch('https://control.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'authkey': msg91AuthKey
        },
        body: JSON.stringify(whatsappPayload)
      })

      const whatsappResult = await whatsappResponse.json()

      if (!whatsappResponse.ok) {
        console.error('WhatsApp API error:', whatsappResult)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send password reset OTP via WhatsApp'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Password reset WhatsApp OTP sent successfully')

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Password reset OTP sent to your WhatsApp'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'verify-otp') {
      if (!otp) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP is required for verification'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Verifying password reset OTP for:', formattedPhone)

      // Use the password reset specific validation function
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_password_reset_otp', { 
          phone_number: formattedPhone, 
          otp_input: otp 
        })

      if (validationError) {
        console.error('Password reset OTP validation error:', validationError)
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'OTP validation failed' 
          }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      const validation = validationResult[0]
      if (!validation || !validation.is_valid) {
        return new Response(
          JSON.stringify({ 
            success: false,
            error: validation?.error_message || 'Invalid or expired OTP' 
          }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      console.log('Password reset OTP verified successfully for user:', validation.user_data?.user_id)

      return new Response(
        JSON.stringify({
          success: true,
          message: 'OTP verified successfully',
          user_data: validation.user_data
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'reset-password') {
      if (!otp || !newPassword) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP and new password are required'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Processing password reset for:', formattedPhone)

      // Verify OTP again for security
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_password_reset_otp', { 
          phone_number: formattedPhone, 
          otp_input: otp 
        })

      if (validationError || !validationResult[0]?.is_valid) {
        console.error('Password reset OTP re-validation failed:', validationError)
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'Invalid or expired OTP' 
          }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      const userData = validationResult[0].user_data
      const userId = userData.user_id

      // Update password
      const { data: passwordResult, error: passwordError } = await supabaseAdmin
        .rpc('update_user_password', { 
          user_id_input: userId, 
          new_password: newPassword 
        })

      if (passwordError || !passwordResult[0]?.success) {
        console.error('Password update failed:', passwordError || passwordResult[0]?.message)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to update password'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Clean up password reset OTP
      await supabaseAdmin
        .from('pending_whatsapp_users')
        .delete()
        .eq('phone', formattedPhone)
        .eq('purpose', 'password_reset')

      console.log('Password reset completed successfully for user:', userId)

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Password reset successfully. You can now login with your new password.'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Forgot password WhatsApp error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
